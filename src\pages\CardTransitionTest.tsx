import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle2, ArrowRight, <PERSON>rk<PERSON>, Heart, Brain, Zap } from 'lucide-react'

interface TestQuestion {
  id: string
  title: string
  description: string
  options: string[]
  icon: React.ReactNode
}

const testQuestions: TestQuestion[] = [
  {
    id: 'energy',
    title: 'How would you describe your energy levels?',
    description: 'Understanding your energy patterns helps us recommend the right supplements',
    options: [
      'Very high - I feel energetic throughout the day',
      'Good - I have steady energy most days',
      'Moderate - I experience some energy dips',
      'Low - I often feel tired and sluggish'
    ],
    icon: <Zap className="h-6 w-6" />
  },
  {
    id: 'sleep',
    title: 'How is your sleep quality?',
    description: 'Quality sleep is fundamental to overall health and supplement effectiveness',
    options: [
      'Excellent - I sleep deeply and wake refreshed',
      'Good - I usually sleep well with minor issues',
      'Fair - I have trouble falling or staying asleep',
      'Poor - I frequently have sleep problems'
    ],
    icon: <Heart className="h-6 w-6" />
  },
  {
    id: 'stress',
    title: 'How do you handle daily stress?',
    description: 'Stress management affects nutrient absorption and supplement needs',
    options: [
      'Very well - I rarely feel overwhelmed',
      'Well - I manage stress effectively most times',
      'Moderately - Stress sometimes affects me',
      'Poorly - I often feel stressed and anxious'
    ],
    icon: <Brain className="h-6 w-6" />
  }
]

export function CardTransitionTest() {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null)
  const [cardKey, setCardKey] = useState(0)
  const [showResults, setShowResults] = useState(false)

  const currentQuestion = testQuestions[currentQuestionIndex]
  const progress = ((currentQuestionIndex + 1) / testQuestions.length) * 100

  const handleAnswer = (answer: string) => {
    if (isTransitioning) return
    
    setSelectedAnswer(answer)
    setIsTransitioning(true)
    
    const newAnswers = { ...answers, [currentQuestion.id]: answer }
    setAnswers(newAnswers)

    setTimeout(() => {
      if (currentQuestionIndex < testQuestions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1)
        setCardKey(prev => prev + 1)
        setSelectedAnswer(null)
        setIsTransitioning(false)
      } else {
        setShowResults(true)
      }
    }, 1000) // Longer delay to showcase the transition
  }

  const resetTest = () => {
    setCurrentQuestionIndex(0)
    setAnswers({})
    setIsTransitioning(false)
    setSelectedAnswer(null)
    setCardKey(0)
    setShowResults(false)
  }

  if (showResults) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl animate-in fade-in-0 slide-in-from-bottom-4 duration-700 shadow-xl border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <div className="flex justify-center mb-4">
              <Sparkles className="h-12 w-12 text-blue-500" />
            </div>
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Test Complete!
            </CardTitle>
            <CardDescription className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mt-4">
              You've experienced the improved card transitions and professional design
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-blue-100 dark:border-gray-600">
              <h3 className="font-semibold text-lg text-blue-900 dark:text-blue-100 mb-3">
                Your Test Answers:
              </h3>
              <div className="space-y-2">
                {Object.entries(answers).map(([questionId, answer]) => {
                  const question = testQuestions.find(q => q.id === questionId)
                  return (
                    <div key={questionId} className="text-sm">
                      <span className="font-medium text-gray-700 dark:text-gray-300">
                        {question?.title}:
                      </span>
                      <span className="text-gray-600 dark:text-gray-400 ml-2">
                        {answer}
                      </span>
                    </div>
                  )
                })}
              </div>
            </div>
            <div className="text-center">
              <Button 
                onClick={resetTest}
                className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <Card 
        key={cardKey}
        className="w-full max-w-2xl transform transition-all duration-700 ease-in-out animate-in fade-in-0 slide-in-from-bottom-4 shadow-xl border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm"
      >
        <CardHeader className="pb-4">
          <div className="space-y-4">
            <div className="flex justify-between text-sm text-muted-foreground font-medium">
              <span>Question {currentQuestionIndex + 1} of {testQuestions.length}</span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <div className="w-full bg-secondary/50 rounded-full h-3 overflow-hidden">
              <div 
                className="bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full transition-all duration-1000 ease-out shadow-sm progress-bar-glow"
                style={{ width: `${progress}%` }}
              />
            </div>
            <div className="flex items-center space-x-3 pt-2">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                {currentQuestion.icon}
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white leading-tight">
                  {currentQuestion.title}
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-300 mt-1">
                  {currentQuestion.description}
                </CardDescription>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-3 pb-6">
          {currentQuestion.options.map((option, index) => {
            const isSelected = selectedAnswer === option
            const isDisabled = isTransitioning
            
            return (
              <Button
                key={index}
                variant="outline"
                disabled={isDisabled}
                className={`
                  w-full justify-start text-left p-6 h-auto min-h-[70px] 
                  transition-all duration-300 ease-in-out transform
                  border-2 rounded-xl font-medium text-base
                  ${isSelected 
                    ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 scale-[1.02] shadow-lg answer-selected' 
                    : 'hover:bg-blue-50 dark:hover:bg-gray-800 hover:border-blue-300 dark:hover:border-blue-600 hover:scale-[1.01] hover:shadow-md border-gray-200 dark:border-gray-700 option-button-hover'
                  }
                  ${isDisabled ? 'opacity-70 cursor-not-allowed' : 'cursor-pointer'}
                  focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                `}
                onClick={() => handleAnswer(option)}
              >
                <div className="flex items-center justify-between w-full">
                  <span className="flex-1 pr-3 leading-relaxed">{option}</span>
                  {isSelected ? (
                    <CheckCircle2 className="h-5 w-5 animate-in zoom-in-50 duration-200" />
                  ) : (
                    <ArrowRight className="h-5 w-5 opacity-0 group-hover:opacity-50 transition-opacity duration-200" />
                  )}
                </div>
              </Button>
            )
          })}
        </CardContent>
      </Card>
    </div>
  )
}

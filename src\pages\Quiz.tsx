import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useQuestions } from '@/hooks/useQuestions'
import { Loader2, CheckCircle2 } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface QuizProps {
  onComplete: (answers: Record<string, any>) => void
}


export function Quiz({ onComplete }: QuizProps) {
  const { questions, loading, error } = useQuestions()
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null)
  const [cardKey, setCardKey] = useState(0)

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="flex items-center justify-center p-8">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading quiz questions...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                Failed to load quiz questions: {error}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    )
  }

  // No questions available
  if (!questions.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Alert>
              <AlertDescription>
                No quiz questions are currently available.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    )
  }

  const currentQuestion = questions[currentQuestionIndex]
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100

  const handleAnswer = (answer: string) => {
    if (isTransitioning) return // Prevent multiple clicks during transition

    setSelectedAnswer(answer)
    setIsTransitioning(true)

    const newAnswers = { ...answers, [currentQuestion.id]: answer }
    setAnswers(newAnswers)

    // Add a delay for visual feedback and smooth transition
    setTimeout(() => {
      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1)
        setCardKey(prev => prev + 1) // Force re-render for animation
        setSelectedAnswer(null)
        setIsTransitioning(false)
      } else {
        onComplete(newAnswers)
      }
    }, 800) // 800ms delay for smooth transition
  }

  // Reset transition state when question changes
  useEffect(() => {
    setIsTransitioning(false)
    setSelectedAnswer(null)
  }, [currentQuestionIndex])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <Card
        key={cardKey}
        className="w-full max-w-2xl transform transition-all duration-500 ease-in-out animate-in fade-in-0 slide-in-from-bottom-4 shadow-xl border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm"
      >
        <CardHeader className="pb-4">
          <div className="space-y-3">
            <div className="flex justify-between text-sm text-muted-foreground font-medium">
              <span>Question {currentQuestionIndex + 1} of {questions.length}</span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <div className="w-full bg-secondary/50 rounded-full h-3 overflow-hidden">
              <div
                className="bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full transition-all duration-700 ease-out shadow-sm"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white leading-tight pt-2 transition-all duration-300">
            {currentQuestion.text}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 pb-6">
          {currentQuestion.options.map((option, index) => {
            const isSelected = selectedAnswer === option
            const isDisabled = isTransitioning

            return (
              <Button
                key={index}
                variant="outline"
                disabled={isDisabled}
                className={`
                  w-full justify-start text-left p-5 h-auto min-h-[60px]
                  transition-all duration-300 ease-in-out transform
                  border-2 rounded-xl font-medium text-base
                  ${isSelected
                    ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 scale-[1.02] shadow-lg'
                    : 'hover:bg-blue-50 dark:hover:bg-gray-800 hover:border-blue-300 dark:hover:border-blue-600 hover:scale-[1.01] hover:shadow-md border-gray-200 dark:border-gray-700'
                  }
                  ${isDisabled ? 'opacity-70 cursor-not-allowed' : 'cursor-pointer'}
                  focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                `}
                onClick={() => handleAnswer(option)}
              >
                <div className="flex items-center justify-between w-full">
                  <span className="flex-1 pr-3">{option}</span>
                  {isSelected && (
                    <CheckCircle2 className="h-5 w-5 animate-in zoom-in-50 duration-200" />
                  )}
                </div>
              </Button>
            )
          })}
        </CardContent>
      </Card>
    </div>
  )
}
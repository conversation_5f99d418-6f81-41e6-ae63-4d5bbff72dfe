import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useQuizResults, type QuizResult } from '@/hooks/useQuizResults'
import { useQuizResponses } from '@/hooks/useQuizResponses'
import { useAuth } from '@/contexts/AuthContext'
import { Loader2 } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface ResultsProps {
  answers: Record<string, any>
  onPayment: () => void
}

interface HealthTag {
  name: string
  description: string
}

interface Recommendation {
  type: 'supplement' | 'food'
  name: string
  description: string
  benefits: string[]
  // Enhanced supplement fields
  side_effects?: string[]
  contraindications?: string[]
  dosage_info?: {
    min_dose?: string
    max_dose?: string
    timing?: string
    form?: string
    with_food?: boolean
  }
  interactions?: string[]
  pregnancy_safe?: boolean
  breastfeeding_safe?: boolean
  rationale?: string
  // Food fields
  serving_suggestions?: string[]
  priority?: number
  score?: number
  // Multi-condition fields
  condition_count?: number
  all_conditions?: string[]
}

// Convert database results to component format
function convertResults(results: QuizResult[]): {
  healthTags: HealthTag[]
  recommendations: Recommendation[]
} {
  const healthTagsMap = new Map<string, HealthTag>()
  const recommendations: Recommendation[] = []

  results.forEach(result => {
    // Collect unique health tags
    if (!healthTagsMap.has(result.health_tag_name)) {
      healthTagsMap.set(result.health_tag_name, {
        name: result.health_tag_name,
        description: result.health_tag_description
      })
    }

    // Add recommendation
    recommendations.push({
      type: result.recommendation_type,
      name: result.recommendation_name,
      description: result.recommendation_details.description,
      benefits: result.recommendation_details.benefits,
      side_effects: result.recommendation_details.side_effects,
      contraindications: result.recommendation_details.contraindications,
      dosage_info: result.recommendation_details.dosage_info,
      interactions: result.recommendation_details.interactions,
      pregnancy_safe: result.recommendation_details.pregnancy_safe,
      breastfeeding_safe: result.recommendation_details.breastfeeding_safe,
      rationale: result.recommendation_details.rationale,
      serving_suggestions: result.recommendation_details.serving_suggestions,
      priority: result.priority,
      score: result.score,
      condition_count: result.condition_count || result.recommendation_details.condition_count,
      all_conditions: result.all_conditions || result.recommendation_details.all_conditions
    })
  })

  return {
    healthTags: Array.from(healthTagsMap.values()),
    recommendations
  }
}

export function Results({ answers, onPayment }: ResultsProps) {
  const { user } = useAuth()
  const { processResults, processing, error: processError } = useQuizResults()
  const { saveResponses, saving, error: saveError } = useQuizResponses()
  const [results, setResults] = useState<QuizResult[]>([])
  const [hasProcessed, setHasProcessed] = useState(false)

  useEffect(() => {
    if (hasProcessed) return

    const handleResults = async () => {
      try {
        console.log('Starting results processing with answers:', answers)
        
        // Save responses to database if user is authenticated (don't await to avoid hanging)
        if (user) {
          saveResponses(answers).catch(err => 
            console.error('Error saving responses:', err)
          )
        }
        
        // Process quiz results
        console.log('Processing quiz results...')
        const quizResults = await processResults(answers)
        console.log('Quiz results processed:', quizResults)
        setResults(quizResults)
        setHasProcessed(true)
      } catch (err) {
        console.error('Error handling quiz results:', err)
        // Set empty results on error so component doesn't hang
        setResults([])
        setHasProcessed(true)
      }
    }

    handleResults()
  }, [answers, user, processResults, saveResponses, hasProcessed])

  // Show loading state
  if (!hasProcessed || processing || saving) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="flex items-center justify-center p-8">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Processing your results...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show error state
  if (processError || saveError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertDescription>
                Failed to process results: {processError || saveError}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    )
  }

  let { healthTags, recommendations } = convertResults(results)

  // Add fallback recommendations if none found
  if (results.length === 0 && hasProcessed && !processing) {
    recommendations = [
      {
        type: 'supplement',
        name: 'Daily Multivitamin',
        description: 'A comprehensive nutritional foundation',
        benefits: ['Fills nutritional gaps', 'Supports overall health', 'Immune function', 'Energy metabolism'],
        dosage_info: {
          min_dose: '1 tablet daily',
          timing: 'Morning with food',
          with_food: true
        },
        rationale: 'General wellness support when specific health areas are not identified'
      },
      {
        type: 'food',
        name: 'Leafy Green Vegetables',
        description: 'Nutrient-dense foundation foods',
        benefits: ['High in folate', 'Rich in iron', 'Antioxidant properties', 'Fiber for gut health'],
        serving_suggestions: ['Aim for 2-3 cups daily', 'Mix into smoothies', 'Sauté with garlic and olive oil']
      }
    ]
  }

  const supplements = recommendations.filter(r => r.type === 'supplement')
  const foods = recommendations.filter(r => r.type === 'food')

  const generateSummary = () => {
    if (healthTags.length > 0) {
      const multiConditionSupplements = supplements.filter(s => s.condition_count && s.condition_count > 1)
      let summary = `Based on your responses, we identified ${healthTags.length} key health area${healthTags.length > 1 ? 's' : ''} to focus on. We recommend ${supplements.length} supplement${supplements.length > 1 ? 's' : ''} and ${foods.length} food recommendation${foods.length > 1 ? 's' : ''} to support your health goals.`
      
      if (multiConditionSupplements.length > 0) {
        summary += ` ${multiConditionSupplements.length} of our supplement recommendations address multiple health areas simultaneously, providing comprehensive support.`
      }
      
      return summary
    }
    return "Based on your responses, you have a solid health foundation. Our recommendations will help you maintain and optimize your wellness."
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card className="animate-in fade-in-0 slide-in-from-bottom-4 duration-700 shadow-xl border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Your Personalized Results
            </CardTitle>
            <CardDescription className="text-lg max-w-3xl mx-auto text-gray-600 dark:text-gray-300 leading-relaxed mt-4">
              {generateSummary()}
            </CardDescription>
          </CardHeader>
        </Card>

        {healthTags.length > 0 && (
          <Card className="animate-in fade-in-0 slide-in-from-bottom-4 duration-700 delay-150 shadow-lg border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">Health Areas to Focus On</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {healthTags.map((tag, index) => (
                <div
                  key={index}
                  className="p-5 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl border border-blue-100 dark:border-gray-600 hover:shadow-md transition-all duration-300"
                >
                  <h3 className="font-semibold text-lg text-blue-900 dark:text-blue-100">{tag.name}</h3>
                  <p className="text-gray-600 dark:text-gray-300 mt-2 leading-relaxed">{tag.description}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        <div className="grid md:grid-cols-2 gap-6">
          {supplements.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recommended Supplements</CardTitle>
                <CardDescription>
                  Preview of supplements that may benefit you
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {supplements.map((rec, index) => (
                  <div key={index} className={`p-4 border rounded-lg ${
                    rec.condition_count && rec.condition_count > 1 ? 'border-primary/50 bg-primary/5' : ''
                  }`}>
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold">{rec.name}</h3>
                      {rec.condition_count && rec.condition_count > 1 && (
                        <div className="flex items-center space-x-1">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary text-primary-foreground">
                            Addresses {rec.condition_count} areas
                          </span>
                        </div>
                      )}
                    </div>
                    {rec.all_conditions && rec.all_conditions.length > 1 && (
                      <div className="mb-2">
                        <p className="text-xs text-primary font-medium">
                          Helps with: {rec.all_conditions.join(', ')}
                        </p>
                      </div>
                    )}
                    <p className="text-sm text-muted-foreground mb-2">{rec.description}</p>
                    <div className="text-sm">
                      <strong>Benefits:</strong>
                      <ul className="list-disc list-inside ml-2">
                        {rec.benefits.slice(0, 2).map((benefit, i) => (
                          <li key={i}>{benefit}</li>
                        ))}
                        {rec.benefits.length > 2 && (
                          <li className="text-muted-foreground">...and more in full report</li>
                        )}
                      </ul>
                    </div>
                    {rec.dosage_info && (
                      <div className="mt-2 text-xs text-muted-foreground bg-secondary/30 p-2 rounded">
                        <strong>Preview dosage:</strong> {rec.dosage_info.min_dose}
                        {rec.dosage_info.timing && ` • ${rec.dosage_info.timing}`}
                        <div className="text-xs mt-1">...full details in report</div>
                      </div>
                    )}
                    {rec.rationale && (
                      <div className="mt-2 text-xs text-blue-600 bg-blue-50 p-2 rounded">
                        <strong>Why recommended:</strong> {rec.rationale.slice(0, 100)}...
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {foods.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Food Recommendations</CardTitle>
                <CardDescription>
                  Nutrient-rich foods to support your health
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {foods.map((rec, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <h3 className="font-semibold">{rec.name}</h3>
                    <p className="text-sm text-muted-foreground mb-2">{rec.description}</p>
                    <div className="text-sm">
                      <strong>Benefits:</strong>
                      <ul className="list-disc list-inside ml-2">
                        {rec.benefits.slice(0, 2).map((benefit, i) => (
                          <li key={i}>{benefit}</li>
                        ))}
                        {rec.benefits.length > 2 && (
                          <li className="text-muted-foreground">...and more in full report</li>
                        )}
                      </ul>
                    </div>
                    {rec.serving_suggestions && rec.serving_suggestions.length > 0 && (
                      <div className="mt-2 text-xs text-green-600 bg-green-50 p-2 rounded">
                        <strong>Serving tip:</strong> {rec.serving_suggestions[0]}
                        {rec.serving_suggestions.length > 1 && <span className="text-muted-foreground"> ...and more tips in report</span>}
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>

        <Card className="bg-primary/5 border-primary/20">
          <CardContent className="text-center space-y-4 pt-6">
            <h3 className="text-2xl font-bold">Get Your Complete Report</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Unlock detailed dosage recommendations, timing guidelines, potential interactions, 
              supplement schedules, and additional wellness tips personalized for your needs.
            </p>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-primary">$2</div>
              <p className="text-sm text-muted-foreground">One-time payment • Instant PDF download</p>
            </div>
            <Button 
              onClick={onPayment}
              size="lg"
              className="text-lg py-6 px-8"
            >
              Get My Complete Report
            </Button>
            <p className="text-xs text-muted-foreground max-w-md mx-auto">
              Your report will include complete dosages, timing recommendations, 
              and a 30-day supplement schedule.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
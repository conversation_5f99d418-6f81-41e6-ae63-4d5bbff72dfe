import jsPDF from 'jspdf'

interface HealthTag {
  name: string
  description: string
}

interface Recommendation {
  type: 'supplement' | 'food'
  name: string
  description: string
  benefits: string[]
  dosage?: string
  timing?: string
}

interface PDFData {
  healthTags: HealthTag[]
  recommendations: Recommendation[]
  answers: Record<string, string>
}

export function generateHealthReportPDF(data: PDFData): void {
  const { healthTags, recommendations } = data
  const supplements = recommendations.filter(r => r.type === 'supplement')
  const foods = recommendations.filter(r => r.type === 'food')

  // Create new PDF document
  const doc = new jsPDF()
  const pageWidth = doc.internal.pageSize.getWidth()
  const pageHeight = doc.internal.pageSize.getHeight()
  let yPosition = 20

  // Helper function to add text with word wrapping
  const addWrappedText = (text: string, x: number, y: number, maxWidth: number, fontSize: number = 10): number => {
    doc.setFontSize(fontSize)
    const lines = doc.splitTextToSize(text, maxWidth)
    doc.text(lines, x, y)
    return y + (lines.length * fontSize * 0.4)
  }

  // Helper function to check if we need a new page
  const checkNewPage = (requiredSpace: number): number => {
    if (yPosition + requiredSpace > pageHeight - 20) {
      doc.addPage()
      return 20
    }
    return yPosition
  }

  // Header
  doc.setFontSize(24)
  doc.setFont('helvetica', 'bold')
  doc.text('Your Personalized Health Report', pageWidth / 2, yPosition, { align: 'center' })
  yPosition += 15

  doc.setFontSize(12)
  doc.setFont('helvetica', 'normal')
  doc.text('Generated by QuizHub', pageWidth / 2, yPosition, { align: 'center' })
  yPosition += 20

  // Date
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
  doc.setFontSize(10)
  doc.text(`Report Date: ${currentDate}`, 20, yPosition)
  yPosition += 20

  // Executive Summary
  yPosition = checkNewPage(30)
  doc.setFontSize(16)
  doc.setFont('helvetica', 'bold')
  doc.text('Executive Summary', 20, yPosition)
  yPosition += 10

  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  let summaryText = ''
  if (healthTags.length > 0) {
    summaryText = `Based on your quiz responses, we identified ${healthTags.length} key health area${healthTags.length > 1 ? 's' : ''} that could benefit from targeted nutritional support. This report provides personalized supplement and food recommendations to help you optimize your health and wellbeing.`
  } else {
    summaryText = 'Based on your responses, you have a solid health foundation. This report provides general wellness recommendations to help you maintain and optimize your health.'
  }
  yPosition = addWrappedText(summaryText, 20, yPosition, pageWidth - 40, 10)
  yPosition += 15

  // Health Areas Section
  if (healthTags.length > 0) {
    yPosition = checkNewPage(40)
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.text('Health Areas to Focus On', 20, yPosition)
    yPosition += 15

    healthTags.forEach((tag, index) => {
      yPosition = checkNewPage(25)
      
      doc.setFontSize(12)
      doc.setFont('helvetica', 'bold')
      doc.text(`${index + 1}. ${tag.name}`, 25, yPosition)
      yPosition += 8

      doc.setFontSize(10)
      doc.setFont('helvetica', 'normal')
      yPosition = addWrappedText(tag.description, 25, yPosition, pageWidth - 50, 10)
      yPosition += 10
    })
  }

  // Supplement Recommendations
  if (supplements.length > 0) {
    yPosition = checkNewPage(40)
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.text('Supplement Recommendations', 20, yPosition)
    yPosition += 15

    supplements.forEach((supplement, index) => {
      yPosition = checkNewPage(60)
      
      doc.setFontSize(12)
      doc.setFont('helvetica', 'bold')
      doc.text(`${index + 1}. ${supplement.name}`, 25, yPosition)
      yPosition += 8

      doc.setFontSize(10)
      doc.setFont('helvetica', 'normal')
      yPosition = addWrappedText(supplement.description, 25, yPosition, pageWidth - 50, 10)
      yPosition += 8

      if (supplement.dosage) {
        doc.setFont('helvetica', 'bold')
        doc.text('Recommended Dosage:', 25, yPosition)
        doc.setFont('helvetica', 'normal')
        yPosition = addWrappedText(supplement.dosage, 70, yPosition, pageWidth - 90, 10)
        yPosition += 5
      }

      if (supplement.timing) {
        doc.setFont('helvetica', 'bold')
        doc.text('Best Time to Take:', 25, yPosition)
        doc.setFont('helvetica', 'normal')
        yPosition = addWrappedText(supplement.timing, 70, yPosition, pageWidth - 90, 10)
        yPosition += 5
      }

      doc.setFont('helvetica', 'bold')
      doc.text('Key Benefits:', 25, yPosition)
      yPosition += 5

      supplement.benefits.forEach((benefit) => {
        doc.setFont('helvetica', 'normal')
        yPosition = addWrappedText(`• ${benefit}`, 30, yPosition, pageWidth - 55, 10)
        yPosition += 3
      })
      
      yPosition += 10
    })
  }

  // Food Recommendations
  if (foods.length > 0) {
    yPosition = checkNewPage(40)
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.text('Food Recommendations', 20, yPosition)
    yPosition += 15

    foods.forEach((food, index) => {
      yPosition = checkNewPage(40)
      
      doc.setFontSize(12)
      doc.setFont('helvetica', 'bold')
      doc.text(`${index + 1}. ${food.name}`, 25, yPosition)
      yPosition += 8

      doc.setFontSize(10)
      doc.setFont('helvetica', 'normal')
      yPosition = addWrappedText(food.description, 25, yPosition, pageWidth - 50, 10)
      yPosition += 8

      doc.setFont('helvetica', 'bold')
      doc.text('Nutritional Benefits:', 25, yPosition)
      yPosition += 5

      food.benefits.forEach((benefit) => {
        doc.setFont('helvetica', 'normal')
        yPosition = addWrappedText(`• ${benefit}`, 30, yPosition, pageWidth - 55, 10)
        yPosition += 3
      })
      
      yPosition += 10
    })
  }

  // 30-Day Implementation Schedule
  yPosition = checkNewPage(60)
  doc.setFontSize(16)
  doc.setFont('helvetica', 'bold')
  doc.text('30-Day Implementation Schedule', 20, yPosition)
  yPosition += 15

  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  
  const scheduleText = [
    'Week 1-2: Start with one supplement at a time to assess tolerance. Begin with the most critical supplement for your primary health concern.',
    'Week 3-4: Add additional supplements gradually. Monitor how you feel and adjust timing if needed.',
    'Ongoing: Maintain consistent supplement routine and incorporate recommended foods into your daily meals.',
    'Monthly Review: Assess progress and adjust dosages or supplements as needed based on how you feel.'
  ]

  scheduleText.forEach((text, index) => {
    yPosition = checkNewPage(15)
    yPosition = addWrappedText(`${index + 1}. ${text}`, 25, yPosition, pageWidth - 50, 10)
    yPosition += 10
  })

  // Important Notes and Disclaimers
  yPosition = checkNewPage(50)
  doc.setFontSize(16)
  doc.setFont('helvetica', 'bold')
  doc.text('Important Notes & Disclaimers', 20, yPosition)
  yPosition += 15

  const disclaimers = [
    'This report is for informational purposes only and is not intended as medical advice.',
    'Always consult with a healthcare professional before starting any new supplement regimen.',
    'Individual results may vary. Start with lower doses and gradually increase as tolerated.',
    'Some supplements may interact with medications. Discuss with your doctor if you take prescription drugs.',
    'Quality matters - choose supplements from reputable manufacturers with third-party testing.',
    'A balanced diet and healthy lifestyle are the foundation of good health.'
  ]

  doc.setFontSize(9)
  doc.setFont('helvetica', 'normal')
  disclaimers.forEach((disclaimer) => {
    yPosition = checkNewPage(10)
    yPosition = addWrappedText(`• ${disclaimer}`, 25, yPosition, pageWidth - 50, 9)
    yPosition += 5
  })

  // Footer
  const totalPages = doc.getNumberOfPages()
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i)
    doc.setFontSize(8)
    doc.setFont('helvetica', 'normal')
    doc.text(`Page ${i} of ${totalPages}`, pageWidth - 40, pageHeight - 10)
    doc.text('Generated by QuizHub - Your Personalized Health Companion', 20, pageHeight - 10)
  }

  // Save the PDF
  const fileName = `QuizHub-Health-Report-${currentDate.replace(/\s/g, '-')}.pdf`
  doc.save(fileName)
}

// Helper function to get user-friendly answer summary
export function generateAnswerSummary(answers: Record<string, string>): string {
  const summaryParts: string[] = []
  
  if (answers['1']) summaryParts.push(`Gender: ${answers['1']}`)
  if (answers['2']) summaryParts.push(`Age: ${answers['2']}`)
  if (answers['3']) summaryParts.push(`Energy Level: ${answers['3']}`)
  if (answers['4']) summaryParts.push(`Sleep Issues: ${answers['4']}`)
  if (answers['5']) summaryParts.push(`Joint Pain: ${answers['5']}`)
  if (answers['6']) summaryParts.push(`Exercise Frequency: ${answers['6']}`)
  if (answers['7']) summaryParts.push(`Diet Type: ${answers['7']}`)
  if (answers['8']) summaryParts.push(`Regular Stress: ${answers['8']}`)
  if (answers['9']) summaryParts.push(`Digestive Health: ${answers['9']}`)
  if (answers['10']) summaryParts.push(`Regular Medications: ${answers['10']}`)

  return summaryParts.join(', ')
}
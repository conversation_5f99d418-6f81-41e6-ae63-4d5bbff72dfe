import { useState } from 'react'
import { useQuizResults } from './useQuizResults'
import { generateHealthReportPDF } from '@/utils/pdfGenerator'

export function useReportGeneration() {
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { processResults } = useQuizResults()

  const generateReport = async (answers: Record<string, string>) => {
    try {
      setGenerating(true)
      setError(null)

      // Process quiz results to get recommendations
      const results = await processResults(answers)

      // Convert results to the format expected by PDF generator
      const healthTagsMap = new Map<string, { name: string; description: string }>()
      const recommendations: Array<{
        type: 'supplement' | 'food'
        name: string
        description: string
        benefits: string[]
        dosage?: string
        timing?: string
      }> = []

      results.forEach(result => {
        // Collect unique health tags
        if (!healthTagsMap.has(result.health_tag_name)) {
          healthTagsMap.set(result.health_tag_name, {
            name: result.health_tag_name,
            description: result.health_tag_description
          })
        }

        // Add recommendation
        recommendations.push({
          type: result.recommendation_type,
          name: result.recommendation_name,
          description: result.recommendation_details.description,
          benefits: result.recommendation_details.benefits,
          dosage: result.recommendation_details.dosage_info?.min_dose,
          timing: result.recommendation_details.dosage_info?.timing
        })
      })

      // Generate PDF
      await generateHealthReportPDF({
        healthTags: Array.from(healthTagsMap.values()),
        recommendations,
        answers
      })

      return true
    } catch (err) {
      console.error('Error generating report:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate report'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setGenerating(false)
    }
  }

  return { generateReport, generating, error }
}
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface LandingProps {
  onStartQuiz: () => void
}

export function Landing({ onStartQuiz }: LandingProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl animate-in fade-in-0 slide-in-from-bottom-4 duration-700 shadow-xl border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm">
        <CardHeader className="text-center pb-6">
          <CardTitle className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Discover Your Ideal Supplements
          </CardTitle>
          <CardDescription className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
            Get personalized supplement recommendations based on your unique health profile
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="grid md:grid-cols-3 gap-6 text-center">
            <div className="space-y-3 p-4 rounded-lg hover:bg-blue-50 dark:hover:bg-gray-800 transition-colors duration-300">
              <div className="text-3xl">⚡</div>
              <h3 className="font-semibold text-lg">Quick & Easy</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                Complete in just 5 minutes
              </p>
            </div>
            <div className="space-y-3 p-4 rounded-lg hover:bg-blue-50 dark:hover:bg-gray-800 transition-colors duration-300">
              <div className="text-3xl">🎯</div>
              <h3 className="font-semibold text-lg">Personalized</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                Tailored to your specific needs
              </p>
            </div>
            <div className="space-y-3 p-4 rounded-lg hover:bg-blue-50 dark:hover:bg-gray-800 transition-colors duration-300">
              <div className="text-3xl">📋</div>
              <h3 className="font-semibold text-lg">Detailed Report</h3>
              <p className="text-sm text-muted-foreground leading-relaxed">
                Comprehensive PDF guide
              </p>
            </div>
          </div>

          <div className="text-center space-y-6">
            <Button
              onClick={onStartQuiz}
              size="lg"
              className="w-full max-w-sm text-lg py-6 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Start Your Health Quiz
            </Button>
            <p className="text-sm text-muted-foreground">
              Get your personalized supplement report for just <span className="font-semibold text-blue-600">$2</span>
            </p>
            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button
                onClick={() => window.location.href = '?test=true'}
                variant="outline"
                size="sm"
                className="text-sm hover:bg-blue-50 dark:hover:bg-gray-800"
              >
                🎨 View Card Design Test
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
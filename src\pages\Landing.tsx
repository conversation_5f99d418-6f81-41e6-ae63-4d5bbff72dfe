import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface LandingProps {
  onStartQuiz: () => void
}

export function Landing({ onStartQuiz }: LandingProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-4xl font-bold mb-2">
            Discover Your Ideal Supplements
          </CardTitle>
          <CardDescription className="text-xl">
            Get personalized supplement recommendations based on your unique health profile
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-3 gap-4 text-center">
            <div className="space-y-2">
              <div className="text-2xl">⚡</div>
              <h3 className="font-semibold">Quick & Easy</h3>
              <p className="text-sm text-muted-foreground">
                Complete in just 5 minutes
              </p>
            </div>
            <div className="space-y-2">
              <div className="text-2xl">🎯</div>
              <h3 className="font-semibold">Personalized</h3>
              <p className="text-sm text-muted-foreground">
                Tailored to your specific needs
              </p>
            </div>
            <div className="space-y-2">
              <div className="text-2xl">📋</div>
              <h3 className="font-semibold">Detailed Report</h3>
              <p className="text-sm text-muted-foreground">
                Comprehensive PDF guide
              </p>
            </div>
          </div>
          
          <div className="text-center space-y-4">
            <Button 
              onClick={onStartQuiz}
              size="lg"
              className="w-full max-w-sm text-lg py-6"
            >
              Start Your Health Quiz
            </Button>
            <p className="text-sm text-muted-foreground">
              Get your personalized supplement report for just $2
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
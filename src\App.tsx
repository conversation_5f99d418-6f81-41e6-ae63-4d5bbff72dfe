import { useState, useEffect } from 'react'
import { Landing } from '@/pages/Landing'
import { Quiz } from '@/pages/Quiz'
import { Results } from '@/pages/Results'
import { CardTransitionTest } from '@/pages/CardTransitionTest'
import { useReportGeneration } from '@/hooks/useReportGeneration'
import { AuthProvider } from '@/contexts/AuthContext'
import { AuthGuard } from '@/components/auth/AuthGuard'

type AppState = 'landing' | 'quiz' | 'results' | 'payment' | 'complete' | 'test'

function App() {
  const [currentState, setCurrentState] = useState<AppState>('landing')
  const [quizAnswers, setQuizAnswers] = useState<Record<string, any>>({})
  const { generateReport, generating } = useReportGeneration()

  // Check for test mode in URL
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('test') === 'true') {
      setCurrentState('test')
    }
  }, [])

  const handleStartQuiz = () => {
    setCurrentState('quiz')
  }

  const handleQuizComplete = (answers: Record<string, any>) => {
    setQuizAnswers(answers)
    setCurrentState('results')
  }

  const handlePayment = async () => {
    setCurrentState('payment')
    // Simulate payment processing
    setTimeout(async () => {
      try {
        await generateReport(quizAnswers)
        setCurrentState('complete')
      } catch (error) {
        console.error('Error generating PDF:', error)
        alert('There was an error generating your report. Please try again.')
        setCurrentState('results')
      }
    }, 2000)
  }

  const renderCurrentState = () => {
    switch (currentState) {
      case 'test':
        return <CardTransitionTest />
      case 'landing':
        return <Landing onStartQuiz={handleStartQuiz} />
      case 'quiz':
        return <Quiz onComplete={handleQuizComplete} />
      case 'results':
        return <Results answers={quizAnswers} onPayment={handlePayment} />
      case 'payment':
        return (
          <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
              <h2 className="text-2xl font-bold">Processing Payment...</h2>
            </div>
          </div>
        )
      case 'complete':
        return (
          <div className="min-h-screen flex items-center justify-center p-4">
            <div className="text-center space-y-4 max-w-2xl">
              <div className="text-6xl">✅</div>
              <h2 className="text-3xl font-bold">Payment Successful!</h2>
              <p className="text-muted-foreground text-lg">
                Your personalized supplement report has been generated and downloaded automatically.
              </p>
              <p className="text-sm text-muted-foreground">
                Check your Downloads folder for your PDF report. If the download didn't start automatically, 
                please check your browser's download settings.
              </p>
              <div className="pt-4">
                <button 
                  className="bg-secondary text-secondary-foreground px-6 py-3 rounded-lg font-semibold mr-4"
                  onClick={() => setCurrentState('landing')}
                >
                  Take Another Quiz
                </button>
                <button 
                  className="bg-primary text-primary-foreground px-6 py-3 rounded-lg font-semibold"
                  onClick={async () => {
                    try {
                      await generateReport(quizAnswers)
                    } catch (error) {
                      console.error('Error generating PDF:', error)
                      alert('There was an error generating your report. Please try again.')
                    }
                  }}
                  disabled={generating}
                >
                  {generating ? 'Generating...' : 'Download Report Again'}
                </button>
              </div>
            </div>
          </div>
        )
      default:
        return <Landing onStartQuiz={handleStartQuiz} />
    }
  }

  return (
    <AuthProvider>
      <AuthGuard>
        {renderCurrentState()}
      </AuthGuard>
    </AuthProvider>
  )
}

export default App
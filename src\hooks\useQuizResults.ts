import { useState, useCallback } from 'react'
import { supabase } from '@/lib/supabase'

export interface QuizResult {
  health_tag_name: string
  health_tag_description: string
  recommendation_type: 'supplement' | 'food'
  recommendation_id: string
  recommendation_name: string
  recommendation_details: {
    description: string
    benefits: string[]
    // Supplement-specific fields
    side_effects?: string[]
    contraindications?: string[]
    dosage_info?: {
      min_dose?: string
      max_dose?: string
      timing?: string
      form?: string
      with_food?: boolean
    }
    interactions?: string[]
    pregnancy_safe?: boolean
    breastfeeding_safe?: boolean
    rationale?: string
    // Multi-condition fields
    all_conditions?: string[]
    condition_count?: number
    // Food-specific fields
    nutritional_info?: any
    serving_suggestions?: string[]
  }
  priority: number
  score: number
  condition_count: number
  all_conditions: string[]
}

export function useQuizResults() {
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const processResults = useCallback(async (answers: Record<string, string>): Promise<QuizResult[]> => {
    try {
      setProcessing(true)
      setError(null)

      console.log('Processing quiz results with answers:', answers)

      // Convert answers to the format expected by the database function
      const userAnswers = Object.fromEntries(
        Object.entries(answers).map(([questionId, answer]) => [questionId, answer])
      )

      console.log('Converted user answers for database:', userAnswers)

      const { data, error: processError } = await supabase
        .rpc('process_quiz_results', { user_answers: userAnswers })

      if (processError) {
        console.error('Database function error:', processError)
        throw processError
      }

      console.log('Database function returned:', data)
      return data || []
    } catch (err) {
      console.error('Error processing quiz results:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to process quiz results'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setProcessing(false)
    }
  }, [])

  return { processResults, processing, error }
}